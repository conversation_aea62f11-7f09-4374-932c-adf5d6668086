'use client';

import { useEffect, useState } from 'react';
import { useImport } from '@/contexts/ImportContext';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { 
  Brain, 
  Sparkles, 
  Upload, 
  FileSearch, 
  Calendar, 
  MapPin, 
  List, 
  Check,
  AlertCircle 
} from 'lucide-react';

const PARSING_STEPS = [
  { id: 'upload', label: 'Uploading conversation', icon: Upload, duration: 1000 },
  { id: 'extract', label: 'Extracting trip details', icon: FileSearch, duration: 2000 },
  { id: 'dates', label: 'Finding dates and times', icon: Calendar, duration: 1500 },
  { id: 'locations', label: 'Identifying locations', icon: MapPin, duration: 2000 },
  { id: 'activities', label: 'Organizing activities', icon: List, duration: 1500 },
  { id: 'optimize', label: 'Creating your itinerary', icon: Sparkles, duration: 1000 }
];

const FUN_FACTS = [
  "Did you know? The average traveler visits 23 websites before booking a trip!",
  "Fun fact: 74% of travelers plan their trips using AI assistants.",
  "Processing tip: Including specific times helps us create a better timeline.",
  "Travel stat: Visual itineraries increase trip satisfaction by 45%.",
  "Almost there! Your organized trip is just moments away."
];

export function ParsingStep() {
  const {
    importId,
    sseUrl,
    setParsedTrip,
    setStep,
    setError,
    parseProgress,
    setParseProgress
  } = useImport();
  
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [overallProgress, setOverallProgress] = useState(0);
  const [funFactIndex, setFunFactIndex] = useState(0);

  useEffect(() => {
    if (!importId) return;

    // Use SSE if available (PDF imports), otherwise fall back to polling
    if (sseUrl) {
      setupSSE();
    } else {
      setupPolling();
    }
  }, [importId, sseUrl]);

  const setupSSE = async () => {
    if (!sseUrl || !importId) return;

    // Get auth token for SSE connection
    const { useAuthStore } = await import('@/stores/auth.store');
    const accessToken = useAuthStore.getState().accessToken;

    if (!accessToken) {
      setError('Authentication required. Please log in again.');
      setStep('input');
      return;
    }

    // Create SSE connection with auth headers
    const eventSource = new EventSource(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${sseUrl}`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        if (data.type === 'progress') {
          setParseProgress({
            step: data.step || 'processing',
            progress: data.progress || 0,
            message: data.message || 'Processing...'
          });

          // Update step index based on progress
          const stepIndex = PARSING_STEPS.findIndex(s => s.id === data.step);
          if (stepIndex !== -1) {
            setCurrentStepIndex(stepIndex);
            setOverallProgress(data.progress || 0);
          }
        } else if (data.type === 'complete') {
          setParsedTrip(data.result);
          setStep('preview');
          eventSource.close();
        } else if (data.type === 'error') {
          setError(data.message || 'Parsing failed. Please try again.');
          setStep('input');
          eventSource.close();
        }
      } catch (error) {
        console.error('Error parsing SSE data:', error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('SSE connection error:', error);
      setError('Connection lost. Please try again.');
      setStep('input');
      eventSource.close();
    };

    // Cleanup on unmount
    return () => {
      eventSource.close();
    };
  };

  const setupPolling = async () => {
    // Import API function
    const { importApi } = await import('@/lib/api/import');

    let pollCount = 0;
    // Increase timeout for PDF parsing - PDFs can take longer to process
    // Check if this is a PDF import by looking for sessionId pattern or source
    const maxPolls = 90; // Maximum 180 seconds (2s * 90) - increased for PDF processing
      
      const pollStatus = async () => {
        try {
          const status = await importApi.getParseStatus(importId);
          
          if (status.status === 'completed' && status.result) {
            setParsedTrip(status.result);
            setStep('preview');
            return true; // Stop polling
          } else if (status.status === 'failed') {
            setError(status.error || 'Parsing failed. Please try again.');
            setStep('input');
            return true; // Stop polling
          } else {
            // Update progress based on status
            if (status.status === 'processing') {
              const progress = Math.min(pollCount * 15, 90); // Simulate progress
              setOverallProgress(progress);
              
              // Update current step based on progress
              const stepIndex = Math.floor((progress / 100) * PARSING_STEPS.length);
              setCurrentStepIndex(Math.min(stepIndex, PARSING_STEPS.length - 1));
              
              setParseProgress({
                step: PARSING_STEPS[stepIndex]?.id || 'processing',
                progress,
                message: `Processing your conversation... ${progress}%`
              });
            }
            
            pollCount++;
            if (pollCount >= maxPolls) {
              // Provide more helpful error message for PDF processing
              const errorMessage = pollCount > 60
                ? 'PDF processing is taking longer than expected. Large or complex PDFs may require more time. Please try again or contact support if the issue persists.'
                : 'Parsing is taking longer than expected. Please try again.';
              setError(errorMessage);
              setStep('input');
              return true; // Stop polling
            }
            
            return false; // Continue polling
          }
        } catch (error) {
          setError('Failed to check parsing status. Please try again.');
          setStep('input');
          return true; // Stop polling
        }
      };

      // Initial poll
      const shouldStop = await pollStatus();
      if (shouldStop) return;

      // Set up polling interval
      const pollInterval = setInterval(async () => {
        const shouldStop = await pollStatus();
        if (shouldStop) {
          clearInterval(pollInterval);
        }
      }, 2000);

      // Rotate fun facts
      const factInterval = setInterval(() => {
        setFunFactIndex(i => (i + 1) % FUN_FACTS.length);
      }, 4000);

      return () => {
        clearInterval(pollInterval);
        clearInterval(factInterval);
      };
    };

    return setupPolling();
  };

  // Simulate progress if no real updates
  useEffect(() => {
    if (!parseProgress) {
      const interval = setInterval(() => {
        setCurrentStepIndex(i => {
          if (i < PARSING_STEPS.length - 1) return i + 1;
          return i;
        });
      }, 2000);

      return () => clearInterval(interval);
    }
    // Add explicit return for the case when parseProgress is truthy
    return undefined;
  }, [parseProgress]);

  const currentStep = PARSING_STEPS[currentStepIndex];

  return (
    <div className="py-8" role="region" aria-label="Import progress">
      <div className="max-w-lg mx-auto">
        {/* Animated Header */}
        <div className="flex justify-center mb-8">
          <div className="relative">
            <div className="absolute inset-0 animate-pulse">
              <div className="w-32 h-32 bg-orange-400 rounded-full opacity-20 blur-xl" />
            </div>
            <div className="relative bg-gradient-to-br from-orange-500 to-pink-500 rounded-full p-6 shadow-lg">
              <Brain className="w-20 h-20 text-white animate-pulse" />
              <Sparkles className="absolute -top-2 -right-2 w-8 h-8 text-yellow-400 animate-spin" />
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <Progress 
            value={overallProgress} 
            className="h-2"
            aria-label="Parsing progress"
            aria-valuenow={overallProgress}
            aria-valuemin={0}
            aria-valuemax={100}
          />
          <p className="text-center text-sm text-gray-600 mt-2" aria-live="polite">
            {Math.round(overallProgress)}% complete
          </p>
        </div>

        {/* Step List */}
        <div 
          className="bg-gray-50 rounded-lg p-6 space-y-4"
          role="list"
          aria-label="Processing steps"
        >
          {PARSING_STEPS.map((step, index) => {
            const isActive = index === currentStepIndex;
            const isComplete = index < currentStepIndex;
            const Icon = step.icon;

            return (
              <div
                key={step.id}
                className={cn(
                  "flex items-center space-x-3 transition-all duration-500",
                  isActive && "scale-105 translate-x-2",
                  !isActive && !isComplete && "opacity-40"
                )}
                role="listitem"
                aria-current={isActive ? 'step' : undefined}
              >
                <div
                  className={cn(
                    "flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300",
                    isComplete && "bg-green-100 text-green-600",
                    isActive && "bg-orange-100 text-orange-600",
                    !isActive && !isComplete && "bg-gray-200 text-gray-400"
                  )}
                >
                  {isComplete ? (
                    <Check className="w-5 h-5" aria-label="Completed" />
                  ) : (
                    <Icon className={cn(
                      "w-5 h-5",
                      isActive && "animate-pulse"
                    )} aria-hidden="true" />
                  )}
                  <span className="sr-only">
                    {isComplete ? 'Completed: ' : isActive ? 'Processing: ' : 'Pending: '}{step.label}
                  </span>
                </div>

                <div className="flex-1">
                  <p className={cn(
                    "font-medium transition-colors",
                    isActive && "text-orange-600",
                    isComplete && "text-green-600",
                    !isActive && !isComplete && "text-gray-500"
                  )}>
                    {step.label}
                    {isActive && (
                      <span className="ml-2 text-orange-500">
                        <span className="animate-pulse">•</span>
                        <span className="animate-pulse animation-delay-200">•</span>
                        <span className="animate-pulse animation-delay-400">•</span>
                      </span>
                    )}
                  </p>
                  {isActive && parseProgress?.message && (
                    <p className="text-sm text-gray-600 mt-1" aria-live="polite">
                      {parseProgress.message}
                    </p>
                  )}
                </div>

                {isActive && (
                  <div className="flex-shrink-0">
                    <div className="w-6 h-6 border-2 border-orange-600 border-t-transparent rounded-full animate-spin" />
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Fun Facts */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600 italic animate-fade-in">
            "{FUN_FACTS[funFactIndex]}"
          </p>
        </div>

        {/* Estimated Time */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            Estimated time remaining: ~{Math.max(1, 6 - currentStepIndex)} seconds
          </p>
        </div>
      </div>
    </div>
  );
}